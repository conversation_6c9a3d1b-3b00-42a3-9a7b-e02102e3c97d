<template>
  <Transition name="chat-dialog-fade" appear @after-leave="handleAfterLeave">
    <div v-if="visible" class="chat-dialog-overlay" @click="handleOverlayClick">
      <div class="chat-dialog" :class="{ show: visible }" @click.stop>
        <!-- 对话头部 -->
        <div class="chat-header">
          <!-- 顶部标题与开关 -->
          <div class="header-bar">
            <h3 class="header-title">与老董聊聊</h3>
            <div class="header-actions">
              <span class="auto-read-label">自动朗读</span>
              <van-switch v-model="autoReadEnabled" :size="28" @change="handleAutoReadToggle" />
            </div>
          </div>

          <div class="header-left" @click="handleHeaderClick">
            <div class="header-indicator"></div>
            <div class="header-arrow">
              <div class="arrow-down"></div>
            </div>
          </div>
        </div>

        <!-- 对话消息区域 -->
        <div ref="chatMessagesRef" class="chat-messages">
          <template v-for="(message, index) in messages" :key="message.key">
            <ChatItem
              :message-data="message"
              :is-regenerate="index === messages.length - 1"
              @regenerate="handleRegenerate"
            />
          </template>

          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="empty-state"></div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue';
import ChatItem from '@/pages/Chat/components/chatItem.vue';
// 导入头像图片 - 统一使用首页的头像文件
import avatar1 from '@/assets/laodong/cat.png';
import avatar2 from '@/assets/laodong/dog.png';
import avatar3 from '@/assets/laodong/man.png';
import avatar4 from '@/assets/laodong/man2.png';
import avatar5 from '@/assets/laodong/woman.png';
import avatar6 from '@/assets/laodong/woman2.png';

// Props
interface IProps {
  visible: boolean;
  messages: IChatStreamContent[];
  conversationId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'regenerate', messageData: IChatStreamContent): void;
  (e: 'new-chat'): void;
}>();

// Refs
const chatMessagesRef = ref<HTMLElement>();



// 自动朗读开关
const autoReadEnabled = ref(false);
const handleAutoReadToggle = (val: boolean) => {
  console.log('🔊 [ChatDialog] 自动朗读切换为:', val);
};



// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
  },
]);

// 立即从localStorage获取初始索引，避免跳变
const getInitialAssistantIndex = (): number => {
  const savedIndex = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const index = savedIndex ? parseInt(savedIndex, 10) : 0;
  console.log('🔍 [ChatDialog.vue] 获取初始助手索引:', { savedIndex, index });
  if (index >= 0 && index < 6) {
    // 助手数组长度为6
    console.log('✅ [ChatDialog.vue] 使用保存的助手索引:', index);
    return index;
  }
  console.log('⚠️ [ChatDialog.vue] 使用默认助手索引: 0');
  return 0;
};

// 响应式的助手索引，立即初始化为正确值
const currentAssistantIndex = ref(getInitialAssistantIndex());

// 获取选中的助手头像
const selectedAssistantAvatar = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    console.log(
      '✅ [ChatDialog.vue] 计算属性返回助手头像:',
      assistants.value[index].name,
      assistants.value[index].avatar,
    );
    return assistants.value[index].avatar;
  }
  console.log(
    '⚠️ [ChatDialog.vue] 计算属性返回默认助手头像:',
    assistants.value[0].name,
    assistants.value[0].avatar,
  );
  return assistants.value[0].avatar;
});

// 处理覆盖层点击（点击上半部分收起）
const handleOverlayClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const dialogElement = target.closest('.chat-dialog');

  // 如果点击的不是对话框内部，则关闭
  if (!dialogElement) {
    console.log('🔽 [ChatDialog] 点击覆盖层，收起对话框');
    emit('close');
  }
};

// 处理头部点击（收起功能）
const handleHeaderClick = () => {
  emit('close');
};

// 处理重新生成
const handleRegenerate = (messageData: IChatStreamContent) => {
  emit('regenerate', messageData);
};













// 处理过渡动画结束后的清理
const handleAfterLeave = () => {
  console.log('🧹 [ChatDialog] 对话框完全关闭，清理状态');
  // 确保body overflow样式被重置
  document.body.style.overflow = '';
  // 强制重新渲染，确保DOM完全清理
  void nextTick(() => {
    console.log('✅ [ChatDialog] DOM清理完成');
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTo({
      top: chatMessagesRef.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    void nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true },
);

// 监听显示状态变化，显示时滚动到底部
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      void nextTick(() => {
        scrollToBottom();
      });
    } else {
      // 确保body overflow样式被重置
      document.body.style.overflow = '';
    }
  },
);
</script>

<style lang="scss" scoped>
// 使用全局主题变量，不再定义本地变量

.chat-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
  pointer-events: auto;
}

.chat-dialog {
  width: 100%;
  max-width: 100vw;
  height: 70vh;
  background: var(--bg-glass-popup); // 使用弹窗专用背景
  border: 2px solid var(--border-accent);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(20px); // 恢复毛玻璃效果
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out forwards;

  &.show {
    animation: slideUp 0.3s ease-out forwards;
  }

  &.hide {
    animation: slideDown 0.3s ease-in forwards;
  }
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-glass);
  display: flex;
  align-items: center;
  position: relative;
  min-height: 100px;

  // 顶部标题/开关栏（不影响点击收起区域）
  .header-bar {
    position: absolute;
    top: 12px;
    left: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    pointer-events: none; // 不拦截 header-left 的点击收起

    .header-title {
      margin: 0;
      padding: 0;
      // 与输入/内容主字体对齐（略小于先前 44px）
      font-size: calc(var(--font-size-2xl) + 6px);
      line-height: 1.2;
      font-weight: 700;
      color: var(--primary-color); // 纯色不透明
      letter-spacing: 0.5px;
      text-shadow: none;
      opacity: 1;
    }

    .header-actions {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      pointer-events: auto; // 允许开关交互

      .auto-read-label {
        font-size: 28px; // 保持放大
        color: var(--primary-color); // 纯色不透明
        text-shadow: none;
        opacity: 1;
      }
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 100%;
    position: relative;

    .header-indicator {
      width: 40px;
      height: 4px;
      background: var(--accent-color);
      border-radius: 2px;
      position: absolute;
      left: 50%;
      top: -8px;
      transform: translateX(-50%);
    }

    .header-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8px;

      .arrow-down {
        width: 0;
        height: 0;
        border-left: 14px solid transparent;
        border-right: 14px solid transparent;
        border-top: 18px solid var(--text-primary);
        transition: transform 0.2s ease;
      }
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  margin-bottom: 225px;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 400;
  }
}



// Transition 动画
.chat-dialog-fade-enter-active,
.chat-dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.chat-dialog-fade-enter-from,
.chat-dialog-fade-leave-to {
  opacity: 0;
}

// 确保离开动画完成后元素完全移除
.chat-dialog-fade-leave-to {
  pointer-events: none;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}
</style>
